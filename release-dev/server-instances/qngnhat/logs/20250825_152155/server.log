2025-08-25T15:21:56.323+07:00  INFO 97782 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 97782 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-08-25T15:21:56.326+07:00  INFO 97782 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-08-25T15:21:57.468+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.552+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 79 ms. Found 22 JPA repository interfaces.
2025-08-25T15:21:57.563+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.565+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-08-25T15:21:57.566+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.576+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 10 JPA repository interfaces.
2025-08-25T15:21:57.578+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.584+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 3 JPA repository interfaces.
2025-08-25T15:21:57.584+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.640+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 56 ms. Found 6 JPA repository interfaces.
2025-08-25T15:21:57.654+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.661+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 1 JPA repository interface.
2025-08-25T15:21:57.669+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.675+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 5 JPA repository interfaces.
2025-08-25T15:21:57.684+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.688+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-08-25T15:21:57.688+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.689+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-25T15:21:57.689+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.695+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-08-25T15:21:57.701+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.706+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 3 JPA repository interfaces.
2025-08-25T15:21:57.710+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.715+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-08-25T15:21:57.716+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.724+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 12 JPA repository interfaces.
2025-08-25T15:21:57.725+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.728+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-08-25T15:21:57.729+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.729+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-25T15:21:57.729+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.730+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-08-25T15:21:57.730+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.738+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 7 JPA repository interfaces.
2025-08-25T15:21:57.739+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.743+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 2 JPA repository interfaces.
2025-08-25T15:21:57.743+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.744+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-25T15:21:57.744+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.761+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 17 ms. Found 19 JPA repository interfaces.
2025-08-25T15:21:57.778+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.785+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 8 JPA repository interfaces.
2025-08-25T15:21:57.786+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.800+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 14 ms. Found 5 JPA repository interfaces.
2025-08-25T15:21:57.802+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.824+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 15 ms. Found 7 JPA repository interfaces.
2025-08-25T15:21:57.827+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.841+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 9 JPA repository interfaces.
2025-08-25T15:21:57.841+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.846+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 6 JPA repository interfaces.
2025-08-25T15:21:57.848+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.852+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-08-25T15:21:57.853+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.860+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 8 JPA repository interfaces.
2025-08-25T15:21:57.860+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.871+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 14 JPA repository interfaces.
2025-08-25T15:21:57.872+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.890+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 18 ms. Found 24 JPA repository interfaces.
2025-08-25T15:21:57.891+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.892+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-08-25T15:21:57.898+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.899+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-25T15:21:57.899+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.908+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 12 JPA repository interfaces.
2025-08-25T15:21:57.911+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.955+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 44 ms. Found 65 JPA repository interfaces.
2025-08-25T15:21:57.955+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.957+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-08-25T15:21:57.963+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T15:21:57.967+07:00  INFO 97782 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-08-25T15:21:58.252+07:00  INFO 97782 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-08-25T15:21:58.256+07:00  INFO 97782 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-08-25T15:21:58.646+07:00  WARN 97782 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-25T15:21:58.927+07:00  INFO 97782 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-08-25T15:21:58.930+07:00  INFO 97782 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-08-25T15:21:58.950+07:00  INFO 97782 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-08-25T15:21:58.950+07:00  INFO 97782 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2434 ms
2025-08-25T15:21:59.019+07:00  WARN 97782 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-25T15:21:59.019+07:00  INFO 97782 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-08-25T15:21:59.158+07:00  INFO 97782 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@7eaa52d6
2025-08-25T15:21:59.158+07:00  INFO 97782 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-08-25T15:21:59.164+07:00  WARN 97782 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-25T15:21:59.164+07:00  INFO 97782 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-08-25T15:21:59.169+07:00  INFO 97782 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@4a3d4cd2
2025-08-25T15:21:59.169+07:00  INFO 97782 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-08-25T15:21:59.169+07:00  WARN 97782 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-25T15:21:59.169+07:00  INFO 97782 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-25T15:21:59.176+07:00  INFO 97782 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@3d1b6816
2025-08-25T15:21:59.176+07:00  INFO 97782 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-25T15:21:59.176+07:00  WARN 97782 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-25T15:21:59.177+07:00  INFO 97782 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-08-25T15:21:59.183+07:00  INFO 97782 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@4f9871a2
2025-08-25T15:21:59.183+07:00  INFO 97782 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-08-25T15:21:59.183+07:00  WARN 97782 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-25T15:21:59.183+07:00  INFO 97782 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-08-25T15:21:59.198+07:00  INFO 97782 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@2ca93dee
2025-08-25T15:21:59.198+07:00  INFO 97782 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-08-25T15:21:59.198+07:00  INFO 97782 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-08-25T15:21:59.262+07:00  INFO 97782 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-08-25T15:21:59.265+07:00  INFO 97782 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@7e64b248{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.13049399533095627252/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@7e4f5062{STARTED}}
2025-08-25T15:21:59.266+07:00  INFO 97782 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@7e64b248{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.13049399533095627252/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@7e4f5062{STARTED}}
2025-08-25T15:21:59.268+07:00  INFO 97782 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@3652ce04{STARTING}[12.0.15,sto=0] @3999ms
2025-08-25T15:21:59.401+07:00  INFO 97782 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-25T15:21:59.442+07:00  INFO 97782 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-25T15:21:59.459+07:00  INFO 97782 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-25T15:21:59.626+07:00  INFO 97782 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-25T15:21:59.823+07:00  WARN 97782 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-25T15:22:00.640+07:00  INFO 97782 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-25T15:22:00.649+07:00  INFO 97782 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@4a9cd434] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-25T15:22:00.818+07:00  INFO 97782 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-25T15:22:01.053+07:00  INFO 97782 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.groovy", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.chatbot", "net.datatp.module.core.print", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-08-25T15:22:01.056+07:00  INFO 97782 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-08-25T15:22:01.064+07:00  INFO 97782 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-25T15:22:01.065+07:00  INFO 97782 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-25T15:22:01.095+07:00  INFO 97782 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-25T15:22:01.101+07:00  WARN 97782 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-25T15:22:03.761+07:00  INFO 97782 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-25T15:22:03.764+07:00  INFO 97782 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@6bd277b2] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-25T15:22:03.944+07:00  WARN 97782 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-08-25T15:22:03.944+07:00  WARN 97782 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-08-25T15:22:03.950+07:00  WARN 97782 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-08-25T15:22:03.950+07:00  WARN 97782 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-08-25T15:22:03.967+07:00  WARN 97782 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-08-25T15:22:03.967+07:00  WARN 97782 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-08-25T15:22:04.349+07:00  INFO 97782 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-25T15:22:04.356+07:00  INFO 97782 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-25T15:22:04.358+07:00  INFO 97782 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-25T15:22:04.375+07:00  INFO 97782 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-25T15:22:04.377+07:00  WARN 97782 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-25T15:22:04.847+07:00  INFO 97782 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-25T15:22:04.848+07:00  INFO 97782 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@266caa63] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-25T15:22:04.898+07:00  WARN 97782 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-08-25T15:22:04.898+07:00  WARN 97782 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-08-25T15:22:05.228+07:00  INFO 97782 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-25T15:22:05.260+07:00  INFO 97782 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-08-25T15:22:05.264+07:00  INFO 97782 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-08-25T15:22:05.264+07:00  INFO 97782 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-25T15:22:05.272+07:00  WARN 97782 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-25T15:22:05.405+07:00  INFO 97782 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-25T15:22:05.863+07:00  INFO 97782 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-08-25T15:22:05.866+07:00  INFO 97782 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-08-25T15:22:05.901+07:00  INFO 97782 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-08-25T15:22:05.945+07:00  INFO 97782 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-08-25T15:22:05.999+07:00  INFO 97782 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-08-25T15:22:06.026+07:00  INFO 97782 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-08-25T15:22:06.046+07:00  INFO 97782 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 625054706ms : this is harmless.
2025-08-25T15:22:06.055+07:00  INFO 97782 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-08-25T15:22:06.058+07:00  INFO 97782 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-08-25T15:22:06.072+07:00  INFO 97782 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 2682227000ms : this is harmless.
2025-08-25T15:22:06.074+07:00  INFO 97782 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-08-25T15:22:06.085+07:00  INFO 97782 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-08-25T15:22:06.086+07:00  INFO 97782 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-08-25T15:22:07.239+07:00  INFO 97782 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-08-25T15:22:07.239+07:00  INFO 97782 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-25T15:22:07.240+07:00  WARN 97782 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-25T15:22:07.895+07:00  INFO 97782 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 25/08/2025@15:15:00+0700 to 25/08/2025@15:30:00+0700
2025-08-25T15:22:07.895+07:00  INFO 97782 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 25/08/2025@15:15:00+0700 to 25/08/2025@15:30:00+0700
2025-08-25T15:22:08.913+07:00  INFO 97782 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-08-25T15:22:08.913+07:00  INFO 97782 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-25T15:22:08.913+07:00  WARN 97782 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-25T15:22:09.129+07:00  INFO 97782 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-08-25T15:22:09.130+07:00  INFO 97782 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-08-25T15:22:09.130+07:00  INFO 97782 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-08-25T15:22:09.130+07:00  INFO 97782 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-08-25T15:22:09.130+07:00  INFO 97782 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-08-25T15:22:10.951+07:00  WARN 97782 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: fddca63a-1bb3-4fb1-83df-9c8a32955d69

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-25T15:22:10.954+07:00  INFO 97782 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-25T15:22:11.275+07:00  INFO 97782 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-08-25T15:22:11.276+07:00  INFO 97782 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-08-25T15:22:11.276+07:00  INFO 97782 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-08-25T15:22:11.276+07:00  INFO 97782 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-08-25T15:22:11.276+07:00  INFO 97782 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-08-25T15:22:11.276+07:00  INFO 97782 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-08-25T15:22:11.276+07:00  INFO 97782 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-08-25T15:22:11.276+07:00  INFO 97782 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-08-25T15:22:11.276+07:00  INFO 97782 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-08-25T15:22:11.276+07:00  INFO 97782 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-08-25T15:22:11.276+07:00  INFO 97782 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-08-25T15:22:11.276+07:00  INFO 97782 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-08-25T15:22:11.279+07:00  INFO 97782 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-08-25T15:22:11.279+07:00  INFO 97782 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-08-25T15:22:11.279+07:00  INFO 97782 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-08-25T15:22:11.330+07:00  INFO 97782 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-25T15:22:11.330+07:00  INFO 97782 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-25T15:22:11.333+07:00  INFO 97782 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-08-25T15:22:11.342+07:00  INFO 97782 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@72a5b54a{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-08-25T15:22:11.342+07:00  INFO 97782 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-08-25T15:22:11.343+07:00  INFO 97782 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-08-25T15:22:11.369+07:00  INFO 97782 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-08-25T15:22:11.369+07:00  INFO 97782 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-08-25T15:22:11.375+07:00  INFO 97782 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 15.573 seconds (process running for 16.106)
2025-08-25T15:23:06.407+07:00  INFO 97782 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-25T15:23:14.432+07:00  INFO 97782 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-25T15:23:14.440+07:00  INFO 97782 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-25T15:23:29.505+07:00  INFO 97782 --- [qtp2129057277-36] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0wl3mqsfoqrjn1l6utp9luv5at0
2025-08-25T15:23:29.949+07:00  INFO 97782 --- [qtp2129057277-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0wl3mqsfoqrjn1l6utp9luv5at0, token = 59439329b84334699c3bb80ab056122f
2025-08-25T15:23:30.346+07:00  INFO 97782 --- [qtp2129057277-36] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-25T15:23:36.148+07:00  INFO 97782 --- [qtp2129057277-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-25T15:23:36.148+07:00  INFO 97782 --- [qtp2129057277-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-25T15:23:55.581+07:00  INFO 97782 --- [qtp2129057277-41] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-25T15:24:03.553+07:00  INFO 97782 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-25T15:24:07.557+07:00  INFO 97782 --- [qtp2129057277-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-25T15:24:20.998+07:00  INFO 97782 --- [qtp2129057277-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-25T15:25:06.682+07:00  INFO 97782 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-25T15:25:06.691+07:00  INFO 97782 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-25T15:25:13.774+07:00  INFO 97782 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-08-25T15:25:13.801+07:00  INFO 97782 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-25T15:25:35.865+07:00  INFO 97782 --- [qtp2129057277-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0wl3mqsfoqrjn1l6utp9luv5at0, token = 59439329b84334699c3bb80ab056122f
2025-08-25T15:25:35.866+07:00  INFO 97782 --- [qtp2129057277-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0wl3mqsfoqrjn1l6utp9luv5at0, token = 59439329b84334699c3bb80ab056122f
2025-08-25T15:25:35.886+07:00  INFO 97782 --- [qtp2129057277-61] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-25T15:25:35.886+07:00  INFO 97782 --- [qtp2129057277-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-25T15:26:02.900+07:00  INFO 97782 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-25T15:27:06.018+07:00  INFO 97782 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-25T15:27:18.118+07:00  INFO 97782 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-08-25T15:27:18.139+07:00  INFO 97782 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-25T15:28:02.223+07:00  INFO 97782 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-25T15:28:05.792+07:00  INFO 97782 --- [qtp2129057277-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0wl3mqsfoqrjn1l6utp9luv5at0, token = 59439329b84334699c3bb80ab056122f
2025-08-25T15:28:05.801+07:00  INFO 97782 --- [qtp2129057277-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-25T15:28:05.823+07:00  INFO 97782 --- [qtp2129057277-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0wl3mqsfoqrjn1l6utp9luv5at0, token = 59439329b84334699c3bb80ab056122f
2025-08-25T15:28:05.831+07:00  INFO 97782 --- [qtp2129057277-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-25T15:29:05.354+07:00  INFO 97782 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-25T15:29:18.398+07:00  INFO 97782 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-08-25T15:29:18.416+07:00  INFO 97782 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-25T15:30:06.494+07:00  INFO 97782 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-25T15:30:06.497+07:00  INFO 97782 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-25T15:30:06.499+07:00  INFO 97782 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-25T15:30:06.505+07:00  INFO 97782 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 25/08/2025@15:30:06+0700
2025-08-25T15:30:06.522+07:00  INFO 97782 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 25/08/2025@15:30:00+0700 to 25/08/2025@15:45:00+0700
2025-08-25T15:30:06.523+07:00  INFO 97782 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 25/08/2025@15:30:00+0700 to 25/08/2025@15:45:00+0700
2025-08-25T15:30:36.259+07:00  INFO 97782 --- [qtp2129057277-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0wl3mqsfoqrjn1l6utp9luv5at0, token = 59439329b84334699c3bb80ab056122f
2025-08-25T15:30:36.262+07:00  INFO 97782 --- [qtp2129057277-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0wl3mqsfoqrjn1l6utp9luv5at0, token = 59439329b84334699c3bb80ab056122f
2025-08-25T15:30:36.274+07:00  INFO 97782 --- [qtp2129057277-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-25T15:30:36.274+07:00  INFO 97782 --- [qtp2129057277-60] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-25T15:30:45.597+07:00  INFO 97782 --- [qtp2129057277-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0wl3mqsfoqrjn1l6utp9luv5at0, token = 59439329b84334699c3bb80ab056122f
2025-08-25T15:30:45.616+07:00  INFO 97782 --- [qtp2129057277-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-25T15:30:45.630+07:00  INFO 97782 --- [qtp2129057277-63] n.d.module.session.ClientSessionManager  : Add a client session id = node0wl3mqsfoqrjn1l6utp9luv5at0, token = 59439329b84334699c3bb80ab056122f
2025-08-25T15:30:45.634+07:00  INFO 97782 --- [qtp2129057277-63] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-25T15:31:04.202+07:00  INFO 97782 --- [qtp2129057277-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0wl3mqsfoqrjn1l6utp9luv5at0, token = 59439329b84334699c3bb80ab056122f
2025-08-25T15:31:04.205+07:00  INFO 97782 --- [qtp2129057277-64] n.d.module.session.ClientSessionManager  : Add a client session id = node0wl3mqsfoqrjn1l6utp9luv5at0, token = 59439329b84334699c3bb80ab056122f
2025-08-25T15:31:04.212+07:00  INFO 97782 --- [qtp2129057277-41] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-25T15:31:04.212+07:00  INFO 97782 --- [qtp2129057277-64] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-25T15:31:04.654+07:00  INFO 97782 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-25T15:31:17.702+07:00  INFO 97782 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-25T15:31:17.714+07:00  INFO 97782 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-25T15:31:24.362+07:00  INFO 97782 --- [qtp2129057277-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0wl3mqsfoqrjn1l6utp9luv5at0, token = 59439329b84334699c3bb80ab056122f
2025-08-25T15:31:24.367+07:00  INFO 97782 --- [qtp2129057277-63] n.d.module.session.ClientSessionManager  : Add a client session id = node0wl3mqsfoqrjn1l6utp9luv5at0, token = 59439329b84334699c3bb80ab056122f
2025-08-25T15:31:24.381+07:00  INFO 97782 --- [qtp2129057277-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-25T15:31:24.381+07:00  INFO 97782 --- [qtp2129057277-63] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-25T15:31:28.142+07:00  INFO 97782 --- [qtp2129057277-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0wl3mqsfoqrjn1l6utp9luv5at0, token = 59439329b84334699c3bb80ab056122f
2025-08-25T15:31:28.143+07:00  INFO 97782 --- [qtp2129057277-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0wl3mqsfoqrjn1l6utp9luv5at0, token = 59439329b84334699c3bb80ab056122f
2025-08-25T15:31:28.154+07:00  INFO 97782 --- [qtp2129057277-41] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-25T15:31:28.155+07:00  INFO 97782 --- [qtp2129057277-36] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-25T15:31:32.627+07:00  INFO 97782 --- [qtp2129057277-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0wl3mqsfoqrjn1l6utp9luv5at0, token = 59439329b84334699c3bb80ab056122f
2025-08-25T15:31:32.630+07:00  INFO 97782 --- [qtp2129057277-41] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-25T15:31:32.644+07:00  INFO 97782 --- [qtp2129057277-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0wl3mqsfoqrjn1l6utp9luv5at0, token = 59439329b84334699c3bb80ab056122f
2025-08-25T15:31:32.648+07:00  INFO 97782 --- [qtp2129057277-69] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-25T15:32:05.688+07:00  INFO 97782 --- [qtp2129057277-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-25T15:32:05.689+07:00  INFO 97782 --- [qtp2129057277-39] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-25T15:32:06.809+07:00  INFO 97782 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-25T15:32:42.587+07:00  INFO 97782 --- [qtp2129057277-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0wl3mqsfoqrjn1l6utp9luv5at0, token = 59439329b84334699c3bb80ab056122f
2025-08-25T15:32:42.589+07:00  INFO 97782 --- [qtp2129057277-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0wl3mqsfoqrjn1l6utp9luv5at0, token = 59439329b84334699c3bb80ab056122f
2025-08-25T15:32:42.605+07:00  INFO 97782 --- [qtp2129057277-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-25T15:32:42.606+07:00  INFO 97782 --- [qtp2129057277-36] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-25T15:32:46.892+07:00  INFO 97782 --- [qtp2129057277-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0wl3mqsfoqrjn1l6utp9luv5at0, token = 59439329b84334699c3bb80ab056122f
2025-08-25T15:32:46.893+07:00  INFO 97782 --- [qtp2129057277-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0wl3mqsfoqrjn1l6utp9luv5at0, token = 59439329b84334699c3bb80ab056122f
2025-08-25T15:32:46.900+07:00  INFO 97782 --- [qtp2129057277-60] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-25T15:32:46.900+07:00  INFO 97782 --- [qtp2129057277-36] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-25T15:32:52.260+07:00  INFO 97782 --- [qtp2129057277-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-25T15:32:52.260+07:00  INFO 97782 --- [qtp2129057277-65] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-25T15:33:03.910+07:00  INFO 97782 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-25T15:33:06.928+07:00  INFO 97782 --- [qtp2129057277-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-25T15:33:17.490+07:00  INFO 97782 --- [qtp2129057277-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-25T15:33:17.965+07:00  INFO 97782 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-08-25T15:33:17.972+07:00  INFO 97782 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-25T15:34:06.044+07:00  INFO 97782 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-25T15:35:03.147+07:00  INFO 97782 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-25T15:35:03.152+07:00  INFO 97782 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-25T15:35:17.185+07:00  INFO 97782 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 2
2025-08-25T15:35:17.189+07:00  INFO 97782 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-25T15:35:41.866+07:00  INFO 97782 --- [qtp2129057277-63] n.d.module.session.ClientSessionManager  : Add a client session id = node0wl3mqsfoqrjn1l6utp9luv5at0, token = 59439329b84334699c3bb80ab056122f
2025-08-25T15:35:41.891+07:00  INFO 97782 --- [qtp2129057277-63] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-25T15:35:41.899+07:00  INFO 97782 --- [qtp2129057277-67] n.d.module.session.ClientSessionManager  : Add a client session id = node0wl3mqsfoqrjn1l6utp9luv5at0, token = 59439329b84334699c3bb80ab056122f
2025-08-25T15:35:41.910+07:00  INFO 97782 --- [qtp2129057277-67] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-25T15:35:46.867+07:00  INFO 97782 --- [qtp2129057277-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0wl3mqsfoqrjn1l6utp9luv5at0, token = 59439329b84334699c3bb80ab056122f
2025-08-25T15:35:46.868+07:00  INFO 97782 --- [qtp2129057277-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0wl3mqsfoqrjn1l6utp9luv5at0, token = 59439329b84334699c3bb80ab056122f
2025-08-25T15:35:46.887+07:00  INFO 97782 --- [qtp2129057277-60] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-25T15:35:46.888+07:00  INFO 97782 --- [qtp2129057277-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-25T15:35:52.289+07:00  INFO 97782 --- [qtp2129057277-39] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-25T15:35:52.289+07:00  INFO 97782 --- [qtp2129057277-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-25T15:36:06.143+07:00  INFO 97782 --- [qtp2129057277-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-25T15:36:06.276+07:00  INFO 97782 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-25T15:37:02.376+07:00  INFO 97782 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-25T15:37:16.443+07:00  INFO 97782 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-08-25T15:37:16.459+07:00  INFO 97782 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-25T15:38:05.541+07:00  INFO 97782 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-25T15:38:33.082+07:00  INFO 97782 --- [qtp2129057277-67] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-25T15:39:06.626+07:00  INFO 97782 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-25T15:39:15.672+07:00  INFO 97782 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-25T15:39:15.678+07:00  INFO 97782 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-25T15:40:04.769+07:00  INFO 97782 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-25T15:40:04.772+07:00  INFO 97782 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-25T15:41:06.875+07:00  INFO 97782 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-25T15:41:14.904+07:00  INFO 97782 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 1
2025-08-25T15:41:14.914+07:00  INFO 97782 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-25T15:41:33.889+07:00  INFO 97782 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@72a5b54a{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-08-25T15:41:33.891+07:00  INFO 97782 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-08-25T15:41:33.891+07:00  INFO 97782 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-08-25T15:41:33.891+07:00  INFO 97782 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-08-25T15:41:33.891+07:00  INFO 97782 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-08-25T15:41:33.891+07:00  INFO 97782 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-08-25T15:41:33.891+07:00  INFO 97782 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-08-25T15:41:33.891+07:00  INFO 97782 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-08-25T15:41:33.891+07:00  INFO 97782 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-08-25T15:41:33.891+07:00  INFO 97782 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-08-25T15:41:33.891+07:00  INFO 97782 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-08-25T15:41:33.891+07:00  INFO 97782 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-08-25T15:41:33.891+07:00  INFO 97782 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-08-25T15:41:33.891+07:00  INFO 97782 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-08-25T15:41:33.892+07:00  INFO 97782 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-08-25T15:41:33.892+07:00  INFO 97782 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-08-25T15:41:33.914+07:00  INFO 97782 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-25T15:41:34.002+07:00  INFO 97782 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-08-25T15:41:34.007+07:00  INFO 97782 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-08-25T15:41:34.062+07:00  INFO 97782 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-25T15:41:34.064+07:00  INFO 97782 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-25T15:41:34.067+07:00  INFO 97782 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-25T15:41:34.068+07:00  INFO 97782 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-25T15:41:34.070+07:00  INFO 97782 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-25T15:41:34.070+07:00  INFO 97782 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-08-25T15:41:34.070+07:00  INFO 97782 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-08-25T15:41:34.070+07:00  INFO 97782 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-25T15:41:34.071+07:00  INFO 97782 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-25T15:41:34.072+07:00  INFO 97782 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-25T15:41:34.073+07:00  INFO 97782 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-25T15:41:34.073+07:00  INFO 97782 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-08-25T15:41:34.074+07:00  INFO 97782 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-08-25T15:41:34.077+07:00  INFO 97782 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@3652ce04{STOPPING}[12.0.15,sto=0]
2025-08-25T15:41:34.088+07:00  INFO 97782 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-25T15:41:34.091+07:00  INFO 97782 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@7e64b248{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.13049399533095627252/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@7e4f5062{STOPPED}}
