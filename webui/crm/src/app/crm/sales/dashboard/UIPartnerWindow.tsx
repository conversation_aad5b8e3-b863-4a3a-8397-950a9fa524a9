import React from 'react';
import * as FeatherIcon from 'react-feather';
import { bs, app } from '@datatp-ui/lib';

import { UICustomerLeadPlugin, UICustomerLeadList } from '../leads/UICustomerLeadList';

import {
  UICustomerMap,
  UICustomerReportPlugin
} from '../partners/UICustomerAnalysisReport';
import { UICustomerLeadPage } from '../leads/UICustomerLeadTree';
import {
  UIBFSOneCustomerListPlugin,
  UIBFSOnePartnerList
} from '../../partner';
import {
  UISalemanKeyAccountReportList, UISalemanKeyAccountReportPlugin
} from '../partners/report/UISalemanKeyAccountReportList';
import { UIPartnerDashboard } from './UIPartnerDashboard';
import { UIBFSOneAgentTransactionsPage } from '../../partner/agenttransaction/UIAgentTransactionsPage';

function createScreenConfig(space: 'User' | 'Company' | 'System') {
  let featureName = space === 'User' ? 'user-logistics-sales' : 'company-logistics-sales';

  const CONFIG: app.space.SpaceConfig = {
    sidebar: {
      width: 200,
    },
    screens: [
      {
        id: 'user-report', label: "Report",
        checkPermission: {
          feature: { module: 'logistics', name: featureName },
          requiredCapability: app.READ,
        },
        screens: [
          ...(space === 'User' ? [{
            id: 'user-saleman-overview', label: 'Overview', icon: FeatherIcon.BarChart2,
            checkPermission: {
              feature: { module: 'logistics', name: featureName },
              requiredCapability: app.READ,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return (<UIPartnerDashboard appContext={appCtx} pageContext={pageCtx} space={space} />)
            }
          }] : []),
          {
            id: 'user-key-account-report-list', label: 'Performances', icon: FeatherIcon.BarChart2,
            checkPermission: {
              feature: { module: 'logistics', name: featureName },
              requiredCapability: app.READ,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              let plugin = new UISalemanKeyAccountReportPlugin('User');
              return <UISalemanKeyAccountReportList appContext={appCtx} pageContext={pageCtx} plugin={plugin} />
            }
          },
        ]
      },
      {
        id: 'customers', label: "Customer",
        checkPermission: {
          feature: { module: 'logistics', name: featureName },
          requiredCapability: app.READ,
        },
        screens: [
          {
            id: 'user-customer-leads', label: 'Customer Leads', icon: FeatherIcon.UserPlus,
            checkPermission: {
              feature: { module: 'logistics', name: featureName },
              requiredCapability: app.READ,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              let plugin: UICustomerLeadPlugin = new UICustomerLeadPlugin(space);
              return (
                space === 'User' ? <UICustomerLeadList appContext={appCtx} pageContext={pageCtx} plugin={plugin} /> :
                  <UICustomerLeadPage appContext={appCtx} pageContext={pageCtx} />
              )
            }
          },
          {
            id: 'user-customers', label: 'Customers', icon: FeatherIcon.Users,
            checkPermission: {
              feature: { module: 'logistics', name: featureName },
              requiredCapability: space !== 'User' ? app.MODERATOR : app.READ,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return <UIBFSOnePartnerList appContext={appCtx} pageContext={pageCtx} partnerType='Customer'
                plugin={new UIBFSOneCustomerListPlugin('Customer', space)} />
            }
          },
          {
            id: 'user-customer-map', label: 'Customer Map', icon: FeatherIcon.Map,
            checkPermission: {
              feature: { module: 'logistics', name: featureName },
              requiredCapability: app.READ,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return <UICustomerMap appContext={appCtx} pageContext={pageCtx}
                plugin={new UICustomerReportPlugin(space)} space={space} />
            }
          }
        ]
      },
      {
        id: 'user-agent', label: "Agent",
        checkPermission: {
          feature: { module: 'logistics', name: featureName },
          requiredCapability: app.READ,
        },
        screens: [
          {
            id: 'user-agent-list', label: 'Agent List', icon: FeatherIcon.Users,
            checkPermission: {
              feature: { module: 'logistics', name: featureName },
              requiredCapability: app.READ,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return <UIBFSOnePartnerList appContext={appCtx} pageContext={pageCtx} partnerType='Agent'
                plugin={new UIBFSOneCustomerListPlugin('Agent', space)} />
            }
          },
          {
            id: 'user-agent-transactions', label: 'Agent Transactions', icon: FeatherIcon.Users,
            checkPermission: {
              feature: { module: 'logistics', name: featureName },
              requiredCapability: app.READ,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return <UIBFSOneAgentTransactionsPage appContext={appCtx} pageContext={pageCtx} />
            }
          },
        ]
      },
      {
        id: 'user-coloader', label: "Coloader",
        checkPermission: {
          feature: { module: 'logistics', name: featureName },
          requiredCapability: app.READ,
        },
        screens: [
          {
            id: 'user-coloader-list', label: 'Coloader List', icon: FeatherIcon.Users,
            checkPermission: {
              feature: { module: 'logistics', name: featureName },
              requiredCapability: app.READ,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return <UIBFSOnePartnerList appContext={appCtx} pageContext={pageCtx} partnerType='Coloader'
                plugin={new UIBFSOneCustomerListPlugin('Coloader', space)} />
            }
          }
        ]
      },
    ]
  }
  return CONFIG;
}

export interface PartnerDashboardPageProps extends app.space.AppSpaceTemplateProps {
  space: 'User' | 'Company' | 'System';
}
export class PartnerDashboardPage extends app.space.AppSpaceTemplate<PartnerDashboardPageProps> {

  protected createConfig(): app.space.SpaceConfig {
    const { space } = this.props;
    return createScreenConfig(space);
  }

  render(): React.JSX.Element {
    if (this.isLoading()) return this.renderLoading();
    const { appContext, pageContext } = this.props;
    let sidebarW = this.config.sidebar?.width;
    if (!sidebarW) sidebarW = 250;

    return (
      <bs.VSplit className="flex-hbox bg-body p-1" smallScreenView="offcanvas">
        <bs.VSplitPane width={sidebarW} title={'Navigation'}
          className="flex-vbox" style={{ backgroundColor: '#f8f9fa', borderRadius: '0.25rem' }}>
          <app.space.AppSpaceSidebar appContext={appContext} pageContext={pageContext} config={this.config} onSelectScreen={this.onSelectScreen} />
        </bs.VSplitPane>
        <bs.VSplitPane title={'Selected Screen'} className="flex-vbox mx-1" style={{ borderRadius: '0.5rem', color: '#4b5563', overflow: "hidden" }}>
          <div key={this.currentSelectScreen.id} className="flex-vbox">
            {this.renderSelectedScreen()}
          </div>
        </bs.VSplitPane>
      </bs.VSplit>
    );
  }
}


