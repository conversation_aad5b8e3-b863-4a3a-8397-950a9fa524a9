import { util } from "@datatp-ui/lib";

class AgentTransactionsDataProcessor {

  calculateShipmentStats = (reportRecordList: any[], shipmentType: string) => {
    const groupByShipmentTypeRecords = reportRecordList.reduce((acc, rec) => {
      const type = rec.shipmentType || '';
      acc[type] = acc[type] || [];
      acc[type].push(rec);
      return acc;
    }, {});

    const records = groupByShipmentTypeRecords[shipmentType] || [];
    let totalHawbCbm = 0;
    let totalHawbGw = 0;
    let totalHawbCw = 0;
    let cont20 = 0;
    let cont40 = 0;
    let cont45 = 0;
    const totalJobCount = records.length;

    for (const rec of records) {
      const service = rec.typeOfService || '';

      // FCL: Tính container
      if (service === 'SeaExpTransactions_FCL' || service === 'SeaImpTransactions_FCL') {
        const containerSize = rec.containerSize || '';
        if (containerSize) {
          const containers = containerSize.split('&');
          for (let cont of containers) {
            cont = cont.trim();
            const idx = cont.indexOf('X');
            if (idx > 0) {
              const quantityStr = cont.substring(0, idx).replace(/[^0-9]/g, '');
              const qty = quantityStr ? parseInt(quantityStr, 10) : 0;
              if (cont.includes('20')) cont20 += qty;
              else if (cont.includes('40')) cont40 += qty;
              else if (cont.includes('45')) cont45 += qty;
            }
          }
        }
      }

      // Air: Tính theo hawbCw
      if (service === 'AirExpTransactions' || service === 'AirImpTransactions') {
        totalHawbCw += Number(rec.hawbCw || 0);
      }

      // LCL/CSL: Tính theo CBM
      if (service === 'SeaExpTransactions_CSL' || service === 'SeaExpTransactions_LCL' ||
        service === 'SeaImpTransactions_CSL' || service === 'SeaImpTransactions_LCL') {
        totalHawbCbm += Number(rec.hawbCbm || 0);
      }

      // Luôn tính GW cho tất cả
      totalHawbGw += Number(rec.hawbGw || 0);
    }

    const totalTeu = cont20 + cont40 * 2 + cont45 * 2;
    return {
      totalJobCount,
      totalHawbCbm,
      totalHawbGw,
      totalHawbCw,
      cont20,
      cont40,
      cont45,
      totalTeu,
    };
  };

  processGroupByAgent(records: any[]): any[] {
    if (!records || records.length === 0) return [];
    let holder: any[] = [];
    const groupedRecords = records.reduce((acc, record) => {
      const agentCode = record.agentCode || '';
      acc[agentCode] = acc[agentCode] || [];
      acc[agentCode].push(record);
      return acc;
    }, {});

    if (Object.keys(groupedRecords).length === 0) {
      return [...records];
    } else {
      for (let agentCode in groupedRecords) {
        const reportRecordList = groupedRecords[agentCode];
        const record = { ...reportRecordList[0] };

        let countryLabel = record.countryLabel;
        if (countryLabel) record.countryLabel = countryLabel.toUpperCase();

        const sortedList = [...reportRecordList].sort((a, b) => {
          const dateStrA = a.reportDate || '';
          const dateStrB = b.reportDate || '';
          const dateA = util.TimeUtil.parseCompactDateTimeFormat(dateStrA);
          const dateB = util.TimeUtil.parseCompactDateTimeFormat(dateStrB);
          return dateB.getTime() - dateA.getTime();
        });

        const latestRecord = sortedList[0];
        record.lastTransactionDate = (latestRecord?.reportDate || '').split('@')[0];
        record.lastTransactionID = latestRecord?.transactionId || '';

        const freehandStats = this.calculateShipmentStats(reportRecordList, 'FREE-HAND');
        record.totalJobCountFH = freehandStats.totalJobCount;
        record.totalHawbCbmFH = freehandStats.totalHawbCbm;
        record.totalHawbGwFH = freehandStats.totalHawbCw;
        record.cont20FH = freehandStats.cont20;
        record.cont40FH = freehandStats.cont40;
        record.cont45FH = freehandStats.cont45;
        record.totalTeuFH = freehandStats.totalTeu;

        const nominatedStats = this.calculateShipmentStats(reportRecordList, 'NOMINATED');
        record.totalJobCountNM = nominatedStats.totalJobCount;
        record.totalHawbCbmNM = nominatedStats.totalHawbCbm;
        record.totalHawbGwNM = nominatedStats.totalHawbCw;
        record.cont20NM = nominatedStats.cont20;
        record.cont40NM = nominatedStats.cont40;
        record.cont45NM = nominatedStats.cont45;
        record.totalTeuNM = nominatedStats.totalTeu;

        holder.push(record);
      }
      return holder;
    }
  }

  processGroupByCountry(records: any[]): any[] {
    if (!records || records.length === 0) return [];
    let holder: any[] = [];
    const groupedRecords = records.reduce((acc, record) => {
      const countryLabel = record.countryLabel || '';
      acc[countryLabel] = acc[countryLabel] || [];
      acc[countryLabel].push(record);
      return acc;
    }, {});

    if (Object.keys(groupedRecords).length === 0) {
      return [...records];
    } else {
      for (let countryLabel in groupedRecords) {
        const reportRecordList = groupedRecords[countryLabel];
        const record = { ...reportRecordList[0] };

        const freehandStats = this.calculateShipmentStats(reportRecordList, 'FREE-HAND');
        record.totalJobCountFH = freehandStats.totalJobCount;
        record.totalHawbCbmFH = freehandStats.totalHawbCbm;
        record.totalHawbGwFH = freehandStats.totalHawbCw;
        record.cont20FH = freehandStats.cont20;
        record.cont40FH = freehandStats.cont40;
        record.cont45FH = freehandStats.cont45;
        record.totalTeuFH = freehandStats.totalTeu;

        const nominatedStats = this.calculateShipmentStats(reportRecordList, 'NOMINATED');
        record.totalJobCountNM = nominatedStats.totalJobCount;
        record.totalHawbCbmNM = nominatedStats.totalHawbCbm;
        record.totalHawbGwNM = nominatedStats.totalHawbCw;
        record.cont20NM = nominatedStats.cont20;
        record.cont40NM = nominatedStats.cont40;
        record.cont45NM = nominatedStats.cont45;
        record.totalTeuNM = nominatedStats.totalTeu;

        holder.push(record);
      }
      return holder;
    }
  }

  processGroupByContinent(records: any[]): any[] {
    if (!records || records.length === 0) return [];
    let holder: any[] = [];
    const groupedRecords = records.reduce((acc, record) => {
      const continent = record.continent || '';
      acc[continent] = acc[continent] || [];
      acc[continent].push(record);
      return acc;
    }, {});

    if (Object.keys(groupedRecords).length === 0) {
      return [...records];
    } else {
      for (let continent in groupedRecords) {
        const reportRecordList = groupedRecords[continent];
        const record = { ...reportRecordList[0] };

        const freehandStats = this.calculateShipmentStats(reportRecordList, 'FREE-HAND');
        record.totalJobCountFH = freehandStats.totalJobCount;
        record.totalHawbCbmFH = freehandStats.totalHawbCbm;
        record.totalHawbGwFH = freehandStats.totalHawbCw;
        record.cont20FH = freehandStats.cont20;
        record.cont40FH = freehandStats.cont40;
        record.cont45FH = freehandStats.cont45;
        record.totalTeuFH = freehandStats.totalTeu;

        const nominatedStats = this.calculateShipmentStats(reportRecordList, 'NOMINATED');
        record.totalJobCountNM = nominatedStats.totalJobCount;
        record.totalHawbCbmNM = nominatedStats.totalHawbCbm;
        record.totalHawbGwNM = nominatedStats.totalHawbCw;
        record.cont20NM = nominatedStats.cont20;
        record.cont40NM = nominatedStats.cont40;
        record.cont45NM = nominatedStats.cont45;
        record.totalTeuNM = nominatedStats.totalTeu;

        holder.push(record);
      }
      return holder;
    }
  }
}


export default AgentTransactionsDataProcessor;
