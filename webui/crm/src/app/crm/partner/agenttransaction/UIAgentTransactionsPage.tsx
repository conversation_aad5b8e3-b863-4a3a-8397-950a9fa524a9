import React from 'react';
import * as FeatherIcon from 'react-feather';
import { bs, grid, app, util, entity, sql } from '@datatp-ui/lib';

import {
  PartnerReportParams,
  WAgentTransactionGroupBySelector,
  WPartnerReportFilter,
  WQuickTimeRangeSelector
} from '../../common/UIDashboardUtility';
import {
  UIAgentTransactionsListPlugin,
  UIAgentTransactions
} from './UIAgentTransactionsList';
import { UIAgentTransactionsByCountry } from './UIAgentTransactionsByCountyList';
import AgentTransactionsDataProcessor from './AgentTransactionsDataProcessor';

export type Space = 'User' | 'Company' | 'System'

export interface AgentTransactionsReportFilter {
  collapsed: boolean;
  groupedBy: { label: string, value: string };
  dateFilter: { fromValue: string, toValue: string, label: string };
  partnerParams: PartnerReportParams;
}

interface UIAgentTransactionsPageState { }
interface UAgentTransactionsPageProps extends app.AppComponentProps { }

export class UIBFSOneAgentTransactionsPage extends app.AppComponent<UAgentTransactionsPageProps, UIAgentTransactionsPageState> {
  viewId: number = util.IDTracker.next();
  reportFilter: AgentTransactionsReportFilter;
  agentTransactionsTreeRef: React.RefObject<UIAgentTransactions>;
  rawRecords: any[] = [];

  constructor(props: app.AppComponentProps) {
    super(props);
    this.agentTransactionsTreeRef = React.createRef<UIAgentTransactions>();

    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    let dateFilter = new util.TimeRange();
    dateFilter.fromSetDate(firstDayOfMonth);
    dateFilter.toSetDate(lastDayOfMonth);

    this.reportFilter = {
      collapsed: false,
      groupedBy: { label: "Country", value: "COUNTRY" },
      dateFilter: { fromValue: dateFilter.fromFormat(), toValue: dateFilter.toFormat(), label: 'This Month' },
      partnerParams: {
        searchPattern: '',
        country: '',
        transactionId: '',
        continent: '',
        source: '',
        fromLocationCode: '',
        toLocationCode: ''
      }
    }
    this.markLoading(true);
  }

  componentDidMount(): void {
    this.loadData();
  }

  loadData(): void {
    const { dateFilter, partnerParams } = this.reportFilter;
    const { appContext } = this.props;
    let plugin = new UIAgentTransactionsListPlugin()
      .withDateFilter('shipmentDate', dateFilter.fromValue, dateFilter.toValue)
      .withSearchPattern(partnerParams.searchPattern)
      .withParams('continent', partnerParams.continent)
      .withParams('source', partnerParams.source)
      .withParams('country', partnerParams.country)
      .withParams('fromLocationCode', partnerParams.fromLocationCode)
      .withParams('toLocationCode', partnerParams.toLocationCode)
      .withParams('transactionId', partnerParams.transactionId);

    appContext.createHttpBackendCall('PartnerReportService', 'searchAgentTransactionsReport', { params: plugin.getSearchParams() })
      .withSuccessData((records: Array<any>) => {
        this.rawRecords = records;
        this.markLoading(false);
        this.forceUpdate();
      })
      .call()
  }

  doExport = () => {
    let { appContext } = this.props;
    let targetRecords = new AgentTransactionsDataProcessor().processGroupByAgent(this.rawRecords);

    let agentGroupField: entity.GroupField = {
      label: "_blank_",
      fields: [
        { label: "STT.", name: "stt", dataType: 'number' },
        { label: "Agent Name", name: "agentName", dataType: 'string' },
        { label: "FH (times)", name: "totalJobCountFH", dataType: 'number' },
        { label: "NM (times)", name: "totalJobCountNM", dataType: 'number' },
        { label: "FCL TEUs(F/H)", name: "totalTeuFH", dataType: 'number' },
        { label: `Cont 20'(F/H)`, name: "cont20FH", dataType: 'number' },
        { label: `Cont 40'(F/H)`, name: "cont40FH", dataType: 'number' },
        { label: `Cont 45'(F/H)`, name: "cont45FH", dataType: 'number' },
        { label: `CBM(F/H)`, name: "totalHawbCbmFH", dataType: 'number' },
        { label: `KGS(F/H)`, name: "totalHawbGwFH", dataType: 'number' },
        { label: "FCL TEUs(N/M)", name: "totalTeuNM", dataType: 'number' },
        { label: `Cont 20'(N/M)`, name: "cont20NM", dataType: 'number' },
        { label: `Cont 40'(N/M)`, name: "cont40NM", dataType: 'number' },
        { label: `Cont 45'(N/M)`, name: "cont45NM", dataType: 'number' },
        { label: `CBM(N/M)`, name: "totalHawbCbmNM", dataType: 'number' },
        { label: `KGS(N/M)`, name: "totalHawbGwNM", dataType: 'number' },
        { label: "Last contact", name: "lastTransactionDate", dataType: 'date' },
        { label: "Last Job ID", name: "lastTransactionID", dataType: 'string' },
        { label: "Source", name: "source", dataType: 'string' },
        { label: "Continent", name: "continent", dataType: 'string' },
        { label: "Country", name: "countryLabel", dataType: 'string', },
      ],
    }

    let records: Array<any> = [];
    for (let i = 0; i < targetRecords.length; i++) {
      let record = targetRecords[i];
      let newRecord: any = {
        stt: i + 1,
        agentName: record.agentName,
        totalJobCountFH: record.totalJobCountFH || 0,
        totalJobCountNM: record.totalJobCountNM || 0,
        totalTeuFH: record.totalTeuFH || 0,
        cont20FH: record.cont20FH || 0,
        cont40FH: record.cont40FH || 0,
        cont45FH: record.cont45FH || 0,
        totalHawbCbmFH: record.totalHawbCbmFH || 0,
        totalHawbGwFH: record.totalHawbGwFH || 0,
        totalTeuNM: record.totalTeuNM || 0,
        cont20NM: record.cont20NM || 0,
        cont40NM: record.cont40NM || 0,
        cont45NM: record.cont45NM || 0,
        totalHawbCbmNM: record.totalHawbCbmNM || 0,
        totalHawbGwNM: record.totalHawbGwNM || 0,
        lastTransactionDate: record.lastTransactionDate || '',
        lastTransactionID: record.lastTransactionID || '',
        source: record.source || '',
        continent: record.continent || '',
        countryLabel: record.countryLabel || '',
      };
      records.push(newRecord);
    }

    let exportModel: entity.DataListExportModel = {
      fieldGroups: [],
      fields: [...agentGroupField.fields],
      records: records,
      modelName: 'Agent_Transactions',
      fileName: `Agent_Transactions_${util.TimeUtil.toDateIdFormat(new Date())}.xlsx`,
    }

    appContext.createHttpBackendCall('DataMappingService', 'xlsxPrivateExport', { model: exportModel })
      .withSuccessData((data: any) => {
        let storeInfo = data;
        if (storeInfo.type == 'ir.actions.act_url') {
          entity.StoreInfo.download(storeInfo.url);
        } else if (storeInfo.privateMode) {
          entity.StoreInfo.privateDownload(storeInfo);
        } else {
          entity.StoreInfo.download(storeInfo.url);
        }
      })
      .call()
  }

  renderHeader() {
    const { appContext, pageContext } = this.props;
    const { dateFilter } = this.reportFilter;
    return (
      <div className="bg-white flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-1">

        <div className="flex-hbox justify-content-start align-items-center" >
          <h5 style={{ color: '#6c757d' }}>
            <FeatherIcon.Users className="me-2" size={18} />
            Agent Transactions
          </h5>

          <div className='flex-hbox align-items-center flex-grow-0 border-start' >
          </div>
        </div>

        <div className="flex-hbox justify-content-end align-items-center gap-1" >

          <WAgentTransactionGroupBySelector appContext={appContext} pageContext={pageContext}
            groupBy={this.reportFilter.groupedBy}
            onModify={(bean: any, _field: string, _oldVal: any, _newVal: any) => {
              this.reportFilter.groupedBy = bean;
              this.markLoading(true);
              this.forceUpdate();
              this.loadData();
            }} />


          <WPartnerReportFilter appContext={appContext} pageContext={pageContext}
            params={this.reportFilter.partnerParams}
            onModify={(bean: any, _field: string, _oldVal: any, _newVal: any) => {
              this.reportFilter.partnerParams = bean;
              this.markLoading(true);
              this.forceUpdate();
              this.loadData();
            }} />

          <WQuickTimeRangeSelector appContext={appContext} pageContext={pageContext}
            initBean={dateFilter}
            onModify={(bean: any, _field: string, _oldVal: any, _newVal: any) => {
              this.reportFilter.dateFilter = bean;
              this.markLoading(true);
              this.forceUpdate();
              this.loadData();
            }} />

          <bs.Button laf="secondary" outline size="sm" className="p-1" style={{ color: '#6c757d' }}
            onClick={this.doExport}>
            <FeatherIcon.Download size={14} className="me-2" />
            Export
          </bs.Button>

        </div>
      </div>
    );
  }


  renderReportView() {
    const { groupedBy } = this.reportFilter;
    const { appContext, pageContext } = this.props;

    if (groupedBy.value === 'CONTINENT') {
      let records: any[] = new AgentTransactionsDataProcessor().processGroupByContinent(this.rawRecords);
      const plugin = new entity.DbEntityListPlugin(records);
      return (
        <UIAgentTransactions ref={this.agentTransactionsTreeRef} rawRecords={this.rawRecords}
          appContext={appContext} pageContext={pageContext} plugin={plugin} />
      )
    } else if (groupedBy.value === 'COUNTRY') {
      let records: any[] = new AgentTransactionsDataProcessor().processGroupByCountry(this.rawRecords);
      const plugin = new entity.DbEntityListPlugin(records);
      return (
        <UIAgentTransactionsByCountry ref={this.agentTransactionsTreeRef} rawRecords={this.rawRecords}
          appContext={appContext} pageContext={pageContext} plugin={plugin} />
      )
    } else {
      let records: any[] = new AgentTransactionsDataProcessor().processGroupByAgent(this.rawRecords);
      const plugin = new entity.DbEntityListPlugin(records);
      return (
        <UIAgentTransactions ref={this.agentTransactionsTreeRef} rawRecords={this.rawRecords}
          appContext={appContext} pageContext={pageContext} plugin={plugin} />
      )
    }

  }

  render(): React.JSX.Element {
    const { appContext, pageContext } = this.props;

    return (
      <div className="flex-vbox mx-1 p-1 bg-white rounded-md w-100 h-100 my-1" >
        {this.renderHeader()}
        <div className="flex-vbox border-top bg-body-highlight h-100" key={util.IDTracker.next()}>
          {this.isLoading()
            ? this.renderLoading()
            : this.renderReportView()
          }
        </div>
      </div>
    )
  }
}
