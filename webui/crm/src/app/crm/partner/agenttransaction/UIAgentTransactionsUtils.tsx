import React from 'react';
import * as FeatherIcon from 'react-feather';
import { grid, sql, entity, input, util } from '@datatp-ui/lib';

import { T } from '../../price';
import { AgentTransactionsReportFilter } from './UIAgentTransactionsPage';

export type Space = 'User' | 'Company' | 'System'

export const agentTransactionsExportFields: entity.GroupField = {
  label: "_blank_",
  fields: [
    { label: "STT.", name: "stt", dataType: 'number' },
    { label: "Agent Name", name: "agent<PERSON><PERSON>", dataType: 'string' },
    { label: "Agent No.", name: "agentCode", dataType: 'string' },

    { label: "Free Hand (Times)", name: "totalJobCountFH", dataType: 'string' },
    { label: "FCL TEUs (F/H)", name: "totalTeuFH", dataType: 'string' },
    { label: "Cont 20' (F/H)", name: "cont20F<PERSON>", dataType: 'string' },
    { label: "Cont 40' (F/H)", name: "cont40FH", dataType: 'string' },
    { label: "Cont 45' (F/H)", name: "cont45FH", dataType: 'string' },
    { label: "CBM (F/H)", name: "totalHawbCbmFH", dataType: 'string' },
    { label: "KGS (F/H)", name: "totalHawbGwFH", dataType: 'string' },

    { label: "Nominated (Times)", name: "totalJobCountNM", dataType: 'string' },
    { label: "FCL TEUs (N/M)", name: "totalTeuNM", dataType: 'string' },
    { label: "Cont 20' (N/M)", name: "cont20NM", dataType: 'string' },
    { label: "Cont 40' (N/M)", name: "cont40NM", dataType: 'string' },
    { label: "Cont 45' (N/M)", name: "cont45NM", dataType: 'string' },
    { label: "CBM (N/M)", name: "totalHawbCbmNM", dataType: 'string' },
    { label: "KGS (N/M)", name: "totalHawbGwNM", dataType: 'string' },

    { label: "Last Date", name: "lastTransactionDate", dataType: 'string' },
    { label: "Latest Job ID", name: "lastTransactionID", dataType: 'string' },
    { label: "Continent", name: "continent", dataType: 'string' },
    { label: "Country", name: "countryLabel", dataType: 'string' },
    { label: "Source", name: "source", dataType: 'string' },
    { label: "Address", name: "address", dataType: 'string' },
    { label: "Work Phone", name: "workPhone", dataType: 'string' },
    { label: "Fax", name: "fax", dataType: 'string' },
  ],
}

export const mapDataExportAgentTransactions = (record: any, root: any) => {
  return {
    ...record,
    agentName: root['agentName'],
    agentCode: root['agentCode'],

    totalJobCountFH: root['totalJobCountFH'],
    totalTeuFH: root['totalTeuFH'],
    cont20FH: root['cont20FH'],
    cont40FH: root['cont40FH'],
    cont45FH: root['cont45FH'],
    totalHawbCbmFH: root['totalHawbCbmFH'],
    totalHawbGwFH: root['totalHawbGwFH'],

    totalJobCountNM: root['totalJobCountNM'],
    totalTeuNM: root['totalTeuNM'],
    cont20NM: root['cont20NM'],
    cont40NM: root['cont40NM'],
    cont45NM: root['cont45NM'],
    totalHawbCbmNM: root['totalHawbCbmNM'],
    totalHawbGwNM: root['totalHawbGwNM'],

    source: root['source'],
    continent: root['continent'],
    countryLabel: root['countryLabel'],
    address: root['address'],
    workPhone: root['workPhone'],
    fax: root['fax'],
    lastTransactionDate: root['lastTransactionDate'],
    lastTransactionID: root['lastTransactionID'],
  }
}


export const countryExportFields: entity.GroupField = {
  label: "_blank_",
  fields: [
    { label: "STT.", name: "stt", dataType: 'number' },
    { label: "Country", name: "countryLabel", dataType: 'string' },
    { label: "Continent", name: "continent", dataType: 'string' },

    { label: "Free Hand (Times)", name: "totalJobCountFH", dataType: 'string' },
    { label: "FCL TEUs (F/H)", name: "totalTeuFH", dataType: 'string' },
    { label: "Cont 20' (F/H)", name: "cont20FH", dataType: 'string' },
    { label: "Cont 40' (F/H)", name: "cont40FH", dataType: 'string' },
    { label: "Cont 45' (F/H)", name: "cont45FH", dataType: 'string' },
    { label: "CBM (F/H)", name: "totalHawbCbmFH", dataType: 'string' },
    { label: "KGS (F/H)", name: "totalHawbGwFH", dataType: 'string' },

    { label: "Nominated (Times)", name: "totalJobCountNM", dataType: 'string' },
    { label: "FCL TEUs (N/M)", name: "totalTeuNM", dataType: 'string' },
    { label: "Cont 20' (N/M)", name: "cont20NM", dataType: 'string' },
    { label: "Cont 40' (N/M)", name: "cont40NM", dataType: 'string' },
    { label: "Cont 45' (N/M)", name: "cont45NM", dataType: 'string' },
    { label: "CBM (N/M)", name: "totalHawbCbmNM", dataType: 'string' },
    { label: "KGS (N/M)", name: "totalHawbGwNM", dataType: 'string' },
  ],
}

export const mapDataExportCountry = (record: any, root: any) => {
  return {
    ...record,
    continent: root['continent'],
    countryLabel: root['label'],
    totalJobCountFH: root['totalJobCountFH'],
    totalTeuFH: root['totalTeuFH'],
    cont20FH: root['cont20FH'],
    cont40FH: root['cont40FH'],
    cont45FH: root['cont45FH'],
    totalHawbCbmFH: root['totalHawbCbmFH'],
    totalHawbGwFH: root['totalHawbGwFH'],

    totalJobCountNM: root['totalJobCountNM'],
    totalTeuNM: root['totalTeuNM'],
    cont20NM: root['cont20NM'],
    cont40NM: root['cont40NM'],
    cont45NM: root['cont45NM'],
    totalHawbCbmNM: root['totalHawbCbmNM'],
    totalHawbGwNM: root['totalHawbGwNM'],
  }
}