package net.datatp.module.resource.location.groovy

import org.springframework.context.ApplicationContext

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.util.ds.MapObject

public class ResourceLocationSql extends Executor {
  static public class SearchCountry extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      String query = """
          SELECT DISTINCT ON (c.id)
            c.id,
            c.code                             AS country_code,
            c.label                            AS country_label,
            g.id                               AS group_id,
            g.label                            AS group_label,
            g.name                             AS group_name,
            parent_g.id                        AS parent_group_id,
            COALESCE(parent_g.label, g.label)  AS parent_group_label,
            parent_g.name                      AS parent_group_name
          FROM settings_country c
          JOIN settings_country_group_rel rel ON rel.country_id = c.id
          JOIN settings_country_group g ON g.id = rel.country_group_id
          LEFT JOIN settings_country_group parent_g ON parent_g.id = SPLIT_PART(g.parent_id_path, '/', 1)::INTEGER
          ORDER BY c.id
      """
      return query;
    }
  }
  
  static public class SearchCountryGroup extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      String query = """
          SELECT
            g.id,
            g.name,
            g.label,
            g.parent_id,
            parent.name AS parent_name,
            parent.label AS parent_label
          FROM settings_country_group g 
          LEFT JOIN settings_country_group parent ON parent.id = g.parent_id
          ORDER BY g.label
      """
      return query;
    }
  }
  
  public ResourceLocationSql() {
    register(new SearchCountry())
    register(new SearchCountryGroup())
  }
}